#!/usr/bin/env python3
"""
Script to inject Afghanistan Farsi (fa-af) prompts into MignalyBot database
This script connects to the database and inserts all required prompt templates for fa-af language
"""

import asyncio
import logging
import sys
import os
from datetime import datetime, timezone

# Add the src directory to the path so we can import from it
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from database.setup import get_async_db, is_sqlite_db
from database.models import PromptTemplate

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Afghanistan Farsi prompts
AFGHANISTAN_PROMPTS = {
    "signals": """د دغه سوداګرۍ سیګنال لپاره د بازار تحلیل او وړاندوینه چمتو کړئ:

سمبول: {symbol}
لارښود: {direction}
د ننوتلو بیه: {entry_price}
د ګټې اخیستنه: {take_profit}
د زیان ودرول: {stop_loss}
د چینل نوم: {channel_brand}

د دغه معاملې لپاره لنډ او مفصل تحلیل ولیکئ چې پکې دا ټولې برخې شاملې وي:
- د بازار اوسنۍ وضعیت
- د دغه سیګنال د ملاتړ دلایل
- د خطر مدیریت لارښودونه
- د ټیکنیکي تحلیل لنډیز

د پیغام په پای کې د چینل هینډل @{channel_handle} اضافه کړئ.

ژبه: افغانستان دری/پښتو""",

    "signal_update": """د دغه سوداګرۍ سیګنال لپاره تازه معلومات چمتو کړئ:

سمبول: {symbol}
لارښود: {direction}
وضعیت: {status}
د ننوتلو بیه: {entry_price}
د وتلو بیه: {exit_price}
ګټه/زیان: {profit_loss}
د چینل نوم: {channel_brand}

د دغه سیګنال د اوسني حالت لپاره لنډ تازه معلومات ولیکئ چې پکې دا ټولې برخې شاملې وي:
- د نتیجې لنډیز
- د بازار د حرکت تحلیل
- د راتلونکي فرصتونو وړاندوینه

د پیغام په پای کې د چینل هینډل @{channel_handle} اضافه کړئ.

ژبه: افغانستان دری/پښتو""",

    "market_analysis": """د دغه بازار لپاره بشپړ تحلیل چمتو کړئ:

سمبول: {symbol}
اوسنۍ بیه: {current_price}
د بیې بدلون: {price_change}%
د چینل نوم: {channel_brand}

د دغه بازار لپاره مفصل تحلیل ولیکئ چې پکې دا ټولې برخې شاملې وي:
- د بازار اوسنۍ رجحان
- د ټیکنیکي شاخصونو تحلیل
- د مهمو ملاتړ او مقاومت کچو پیژندنه
- د راتلونکي حرکت وړاندوینه
- د سوداګرۍ فرصتونو ارزونه

د پیغام په پای کې د چینل هینډل @{channel_handle} اضافه کړئ.

ژبه: افغانستان دری/پښتو""",

    "news": """د دغه اقتصادي خبر لپاره د بازار اغیزو تحلیل چمتو کړئ:

د خبر سرلیک: {title}
د خبر متن: {content}
د چینل نوم: {channel_brand}

د دغه خبر لپاره لنډ او مفید تحلیل ولیکئ چې پکې دا ټولې برخې شاملې وي:
- د خبر د اهمیت ارزونه
- د مالي بازارونو پر اغیزې
- د سوداګرۍ فرصتونو پیژندنه
- د خطر مدیریت لارښودونه

د پیغام په پای کې د چینل هینډل @{channel_handle} اضافه کړئ.

ژبه: افغانستان دری/پښتو""",

    "events": """د دغه اقتصادي پیښې لپاره د بازار اغیزو تحلیل چمتو کړئ:

د پیښې نوم: {title}
هیواد: {country}
اهمیت: {impact}
وخت: {event_time}
تمه: {forecast}
پخوانۍ ارزښت: {previous}
اصلي ارزښت: {actual}
د چینل نوم: {channel_brand}

د دغه اقتصادي پیښې لپاره مفصل تحلیل ولیکئ چې پکې دا ټولې برخې شاملې وي:
- د پیښې د اهمیت ارزونه
- د تمه او اصلي ارزښت ترمنځ پرتله
- د اسعارو پر اغیزې
- د سوداګرۍ فرصتونو وړاندوینه

د پیغام په پای کې د چینل هینډل @{channel_handle} اضافه کړئ.

ژبه: افغانستان دری/پښتو""",

    "performance": """د دغه سوداګرۍ سیګنال د فعالیت راپور چمتو کړئ:

سمبول: {symbol}
لارښود: {direction}
د ننوتلو بیه: {entry_price}
د وتلو بیه: {exit_price}
ګټه/زیان: {profit_loss}%
د چینل نوم: {channel_brand}

د دغه سیګنال د فعالیت لپاره بشپړ راپور ولیکئ چې پکې دا ټولې برخې شاملې وي:
- د نتیجې لنډیز
- د فعالیت د کیفیت ارزونه
- د بازار د حرکت تحلیل
- د راتلونکي ښه والي لپاره وړاندیزونه

د پیغام په پای کې د چینل هینډل @{channel_handle} اضافه کړئ.

ژبه: افغانستان دری/پښتو""",

    "greeting": """د ورځې د پیل لپاره ګرم هرکلي پیغام چمتو کړئ:

د چینل نوم: {channel_brand}

د نننۍ ورځې لپاره ګرم او مثبت هرکلي پیغام ولیکئ چې پکې دا ټولې برخې شاملې وي:
- د ورځې ښه هیلې
- د بازار د فرصتونو یادونه
- د پیروانو هڅونه
- د بریالیتوب غوښتنه

د پیغام په پای کې د چینل هینډل @{channel_handle} اضافه کړئ.

ژبه: افغانستان دری/پښتو""",

    "analysis": """د دغه مالي وسیلې لپاره بشپړ ټیکنیکي تحلیل چمتو کړئ:

سمبول: {symbol}
د چارټ معلومات: {chart_data}
د چینل نوم: {channel_brand}

د دغه مالي وسیلې لپاره مفصل ټیکنیکي تحلیل ولیکئ چې پکې دا ټولې برخې شاملې وي:
- د اوسني رجحان ارزونه
- د مهمو ټیکنیکي کچو پیژندنه
- د شاخصونو تحلیل
- د راتلونکي حرکت وړاندوینه
- د سوداګرۍ فرصتونو وړاندیز

د پیغام په پای کې د چینل هینډل @{channel_handle} اضافه کړئ.

ژبه: افغانستان دری/پښتو"""
}

async def inject_prompts():
    """Inject Afghanistan Farsi prompts into the database"""
    logger.info("🚀 Starting Afghanistan Farsi prompts injection...")
    
    try:
        async for db in get_async_db():
            success_count = 0
            error_count = 0
            
            for prompt_type, content in AFGHANISTAN_PROMPTS.items():
                try:
                    # Check if prompt already exists using SQLAlchemy ORM
                    from sqlalchemy import select
                    stmt = select(PromptTemplate).where(
                        PromptTemplate.post_type == prompt_type,
                        PromptTemplate.language == "fa-af"
                    )

                    if is_sqlite_db():
                        result = db.execute(stmt)
                        existing = result.fetchone()
                    else:
                        result = await db.execute(stmt)
                        existing = await result.fetchone()

                    if existing:
                        logger.info(f"⚠️ Prompt {prompt_type} (fa-af) already exists, skipping...")
                        continue
                    
                    # Create new prompt template
                    prompt_template = PromptTemplate(
                        post_type=prompt_type,
                        language="fa-af",
                        template_content=content,
                        created_at=datetime.now(timezone.utc)
                    )
                    
                    db.add(prompt_template)
                    success_count += 1
                    logger.info(f"✅ Added prompt: {prompt_type} (fa-af)")
                    
                except Exception as e:
                    error_count += 1
                    logger.error(f"❌ Error adding prompt {prompt_type} (fa-af): {e}")
            
            # Commit all changes
            if is_sqlite_db():
                db.commit()
            else:
                await db.commit()
            
            logger.info(f"🎉 Injection completed! Success: {success_count}, Errors: {error_count}")
            
            if success_count > 0:
                logger.info("✅ Afghanistan Farsi prompts have been successfully injected into the database!")
            
            break  # Exit the async generator loop
            
    except Exception as e:
        logger.error(f"💥 Fatal error during injection: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(inject_prompts())
