#!/usr/bin/env python3
"""
Script to inject Afghanistan Farsi (fa-af) prompts into MignalyBot database
This script connects to the database and inserts all required prompt templates for fa-af language
"""

import asyncio
import logging
import sys
import os
from datetime import datetime, timezone

# Add the src directory to the path so we can import from it
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from database.setup import get_async_db, is_sqlite_db
from database.models import PromptTemplate

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Afghanistan Farsi prompts
AFGHANISTAN_PROMPTS = {
    "signals": """برای این سیگنال معاملاتی تحلیل بازار و پیش‌بینی ارائه دهید:

نماد: {symbol}
جهت: {direction}
قیمت ورود: {entry_price}
هدف سود: {take_profit}
حد ضرر: {stop_loss}
نام کانال: {channel_brand}

برای این معامله تحلیل کوتاه و مفصل بنویسید که شامل این بخش‌ها باشد:
- وضعیت فعلی بازار
- دلایل حمایت از این سیگنال
- راهنمایی‌های مدیریت ریسک
- خلاصه تحلیل تکنیکال

در پایان پیام هندل کانال @{channel_handle} را اضافه کنید.

زبان: فارسی دری افغانستان""",

    "signal_update": """برای این سیگنال معاملاتی اطلاعات جدید ارائه دهید:

نماد: {symbol}
جهت: {direction}
وضعیت: {status}
قیمت ورود: {entry_price}
قیمت خروج: {exit_price}
سود/زیان: {profit_loss}
نام کانال: {channel_brand}

برای وضعیت فعلی این سیگنال اطلاعات کوتاه و جدید بنویسید که شامل این بخش‌ها باشد:
- خلاصه نتیجه
- تحلیل حرکت بازار
- پیش‌بینی فرصت‌های آینده

در پایان پیام هندل کانال @{channel_handle} را اضافه کنید.

زبان: فارسی دری افغانستان""",

    "market_analysis": """برای این بازار تحلیل کامل ارائه دهید:

نماد: {symbol}
قیمت فعلی: {current_price}
تغییر قیمت: {price_change}%
نام کانال: {channel_brand}

برای این بازار تحلیل مفصل بنویسید که شامل این بخش‌ها باشد:
- روند فعلی بازار
- تحلیل شاخص‌های تکنیکال
- شناسایی سطوح مهم حمایت و مقاومت
- پیش‌بینی حرکت آینده
- ارزیابی فرصت‌های معاملاتی

در پایان پیام هندل کانال @{channel_handle} را اضافه کنید.

زبان: فارسی دری افغانستان""",

    "news": """برای این خبر اقتصادی تحلیل تأثیرات بازار ارائه دهید:

عنوان خبر: {title}
متن خبر: {content}
نام کانال: {channel_brand}

برای این خبر تحلیل کوتاه و مفید بنویسید که شامل این بخش‌ها باشد:
- ارزیابی اهمیت خبر
- تأثیرات بر بازارهای مالی
- شناسایی فرصت‌های معاملاتی
- راهنمایی‌های مدیریت ریسک

در پایان پیام هندل کانال @{channel_handle} را اضافه کنید.

زبان: فارسی دری افغانستان""",

    "events": """برای این رویداد اقتصادی تحلیل تأثیرات بازار ارائه دهید:

نام رویداد: {title}
کشور: {country}
اهمیت: {impact}
زمان: {event_time}
پیش‌بینی: {forecast}
مقدار قبلی: {previous}
مقدار واقعی: {actual}
نام کانال: {channel_brand}

برای این رویداد اقتصادی تحلیل مفصل بنویسید که شامل این بخش‌ها باشد:
- ارزیابی اهمیت رویداد
- مقایسه بین پیش‌بینی و مقدار واقعی
- تأثیرات بر قیمت‌ها
- پیش‌بینی فرصت‌های معاملاتی

در پایان پیام هندل کانال @{channel_handle} را اضافه کنید.

زبان: فارسی دری افغانستان""",

    "performance": """برای این سیگنال معاملاتی گزارش عملکرد ارائه دهید:

نماد: {symbol}
جهت: {direction}
قیمت ورود: {entry_price}
قیمت خروج: {exit_price}
سود/زیان: {profit_loss}%
نام کانال: {channel_brand}

برای عملکرد این سیگنال گزارش کامل بنویسید که شامل این بخش‌ها باشد:
- خلاصه نتیجه
- ارزیابی کیفیت عملکرد
- تحلیل حرکت بازار
- پیشنهادات برای بهبود آینده

در پایان پیام هندل کانال @{channel_handle} را اضافه کنید.

زبان: فارسی دری افغانستان""",

    "greeting": """برای شروع روز پیام خوش‌آمدگویی گرم ارائه دهید:

نام کانال: {channel_brand}

برای روز امروز پیام گرم و مثبت خوش‌آمدگویی بنویسید که شامل این بخش‌ها باشد:
- آرزوهای خوب برای روز
- اشاره به فرصت‌های بازار
- تشویق پیروان
- آرزوی موفقیت

در پایان پیام هندل کانال @{channel_handle} را اضافه کنید.

زبان: فارسی دری افغانستان""",

    "analysis": """برای این ابزار مالی تحلیل تکنیکال کامل ارائه دهید:

نماد: {symbol}
اطلاعات چارت: {chart_data}
نام کانال: {channel_brand}

برای این ابزار مالی تحلیل تکنیکال مفصل بنویسید که شامل این بخش‌ها باشد:
- ارزیابی روند فعلی
- شناسایی سطوح مهم تکنیکال
- تحلیل شاخص‌ها
- پیش‌بینی حرکت آینده
- پیشنهاد فرصت‌های معاملاتی

در پایان پیام هندل کانال @{channel_handle} را اضافه کنید.

زبان: فارسی دری افغانستان"""
}

async def inject_prompts():
    """Inject Afghanistan Farsi prompts into the database"""
    logger.info("🚀 Starting Afghanistan Farsi prompts injection...")
    
    try:
        async for db in get_async_db():
            success_count = 0
            error_count = 0
            
            for prompt_type, content in AFGHANISTAN_PROMPTS.items():
                try:
                    # Check if prompt already exists using SQLAlchemy ORM
                    from sqlalchemy import select
                    stmt = select(PromptTemplate).where(
                        PromptTemplate.post_type == prompt_type,
                        PromptTemplate.language == "fa-af"
                    )

                    if is_sqlite_db():
                        result = db.execute(stmt)
                        existing = result.fetchone()
                    else:
                        result = await db.execute(stmt)
                        existing = await result.fetchone()

                    if existing:
                        logger.info(f"⚠️ Prompt {prompt_type} (fa-af) already exists, skipping...")
                        continue
                    
                    # Create new prompt template
                    prompt_template = PromptTemplate(
                        post_type=prompt_type,
                        language="fa-af",
                        template_content=content,
                        created_at=datetime.now(timezone.utc)
                    )
                    
                    db.add(prompt_template)
                    success_count += 1
                    logger.info(f"✅ Added prompt: {prompt_type} (fa-af)")
                    
                except Exception as e:
                    error_count += 1
                    logger.error(f"❌ Error adding prompt {prompt_type} (fa-af): {e}")
            
            # Commit all changes
            if is_sqlite_db():
                db.commit()
            else:
                await db.commit()
            
            logger.info(f"🎉 Injection completed! Success: {success_count}, Errors: {error_count}")
            
            if success_count > 0:
                logger.info("✅ Afghanistan Farsi prompts have been successfully injected into the database!")
            
            break  # Exit the async generator loop
            
    except Exception as e:
        logger.error(f"💥 Fatal error during injection: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(inject_prompts())
